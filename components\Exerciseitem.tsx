import React, { useRef, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';

interface Exercise {
  id: string;
  name_al: string;
  name_en: string;
  description_al: string;
  description_en: string;
  video_url: string;
  image_url: string;
  sets: number;
  reps: number;
  rest_between_sets: number;
  instructions: {
    sq: string[];
    en: string[];
  };
  name?: string;
}

interface ExerciseItemProps {
  exercise: Exercise;
  weight: string;
  isHighlighted: boolean;
  isFocused: boolean;
  onWeightChange: (value: string) => void;
  onFocus: () => void;
  onBlur: () => void;
}

const ExerciseItem = ({ 
  exercise, 
  weight, 
  isHighlighted, 
  isFocused, 
  onWeightChange, 
  onFocus, 
  onBlur 
}: ExerciseItemProps) => {
  const inputRef = useRef(null);
  const [isInputFocused, setIsInputFocused] = React.useState(false);

  // Debug log
  console.log('ExerciseItem received exercise:', exercise);

  const handleFocus = () => {
    setIsInputFocused(true);
    if (onFocus) onFocus();
  };

  const handleChangeText = (text: string) => {
    // Only allow numbers and decimal point
    const numericText = text.replace(/[^0-9.]/g, '');
    onWeightChange(numericText);
  };

  const handleBlur = () => {
    setIsInputFocused(false);
    if (onBlur) onBlur();
  };

  // Function to get display value
  const getDisplayValue = () => {
    if (isInputFocused) {
      // When focused, show only numbers or empty string
      return weight === 'Pesha' ? '' : weight;
    } else {
      // When not focused, show "Pesha" if no weight is set
      return weight === '' || weight === '0' || weight === undefined || weight === null ? 'Pesha' : weight;
    }
  };

  return (
    <View style={[ 
      styles.scrollItem, 
      isHighlighted 
        ? { backgroundColor: 'rgba(238, 242, 245, 1)' } 
        : { backgroundColor: '#ffffff' } 
    ]}>
      {/* Left side: Play icon and text content */}
      <View style={styles.leftContent}>
        {isHighlighted && (
          <View style={styles.playIconWrapper}>
            <TouchableOpacity style={styles.playIcon}>
              <FontAwesome5 name={'play'} size={8} color={'#4B5563'} />
            </TouchableOpacity>
          </View>
        )}
        
        <View style={styles.textContainer}>
          <Text
            style={[
              styles.scrollText,
              isHighlighted
                ? { fontWeight: 'bold', color: '#000000' }
                : { fontWeight: 'normal', color: '#9CA3AF' },
            ]}
          >
            {exercise.name || exercise.name_al || 'No Name'}
          </Text>
          
          {/* Exercise details in a more compact layout */}
          {/* You can add more details here if needed, but only the name is required as per instructions */}
        </View>
      </View>

      {/* Right side: Weight input */}
      <View style={styles.rightContent}>
      <TextInput
  ref={inputRef}
  style={[
    styles.weightInput,
    isHighlighted && styles.highlightedInput
  ]}
  keyboardType="numeric"
  value={weight}
  onFocus={handleFocus}
  onBlur={handleBlur}
  onChangeText={handleChangeText}
  scrollEnabled={false}
  multiline={false}
  maxLength={5}
  placeholder="Pesha"
  placeholderTextColor="#9CA3AF"
/>

      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  scrollItem: {
    flexDirection: 'row',
    alignItems: 'center',          
    justifyContent: 'space-between',
    paddingVertical: 0,            // Already at minimum
    paddingHorizontal: 12,         // Unchanged
    borderRadius: 8,               // Unchanged
    marginBottom: 1,               // Reduced from 2 to 1
    minHeight: 22,                 // Reduced from 28 to 22
    width: '100%',
  },    
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  playIconWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,                // Reduced from 6 to 4
  },
  playIcon: {
    width: 11,                     // Reduced from 14 to 11
    height: 11,                    // Reduced from 14 to 11
    borderRadius: 5.5,             // Reduced from 7 to 5.5
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  scrollText: {
    fontSize: 20,                  // Increased font size
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 0,
    lineHeight: 26,                // Increased line height for better spacing
  },
  exerciseDetails: {
    marginBottom: 0,               // Unchanged
  },
  detailText: {
    fontSize: 16,                  // Increased font size
    color: '#9CA3AF',
    lineHeight: 22,                // Increased line height for better spacing
  },
  rightContent: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,                  // Reduced from 50 to 40
  },
  weightLabel: {
    fontSize: 10,                  // Reduced from 12 to 10
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 1,               // Reduced from 2 to 1
  },
  weightInput: {
    width: 34,                     // Reduced from 44 to 34
    height: 18,                    // Reduced from 24 to 18
    borderWidth: 0,
    borderRadius: 6,               // Unchanged
    paddingHorizontal: 2,          // Reduced from 4 to 2
    paddingVertical: 0,    
    textAlign: 'center',   
    textAlignVertical: 'center', 
    fontSize: 11,                  // Reduced from 14 to 11
    fontWeight: '600',
    backgroundColor: '#ffffff',
    color: '#9CA3AF',
    includeFontPadding: false, 
    lineHeight: 12,                // Reduced from 16 to 12
  },
  
  highlightedInput: {
    backgroundColor: 'rgba(238, 242, 245, 1)',
    color: '#000000',
    fontWeight: 'bold',
  },
});

export default ExerciseItem;