import React, { useState } from "react";
import { StyleSheet, Dimensions, View, TouchableOpacity, Text, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Feather } from "@expo/vector-icons";
import { WebView } from "react-native-webview";
import * as Sharing from "expo-sharing";

export default function PdfViewer() {
  const { uri } = useLocalSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  console.log("PdfVie<PERSON> received URI:", uri);

  const handleSharePdf = async () => {
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri);
      } else {
        alert('Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Error sharing PDF:', error);
      alert('Failed to share PDF');
    }
  };

  if (!uri) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Feather name="arrow-left" size={24} color="#C14242" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Recipe</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>No PDF file specified</Text>
        </View>
      </SafeAreaView>
    );
  }

  // For iOS, WebView can display PDFs directly
  // For Android, we'll provide a share option to open in external PDF viewer
  const shouldUseWebView = Platform.OS === 'ios';

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Feather name="arrow-left" size={24} color="#C14242" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Recipe</Text>
        <TouchableOpacity
          style={styles.shareButton}
          onPress={handleSharePdf}
        >
          <Feather name="share" size={20} color="#C14242" />
        </TouchableOpacity>
      </View>

      {shouldUseWebView ? (
        // iOS: Use WebView to display PDF
        <WebView
          source={{ uri: uri }}
          style={styles.webview}
          onLoadStart={() => setIsLoading(true)}
          onLoadEnd={() => setIsLoading(false)}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.error('WebView error:', nativeEvent);
            setError('Failed to load PDF');
            setIsLoading(false);
          }}
          startInLoadingState={true}
          renderLoading={() => (
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>Loading PDF...</Text>
            </View>
          )}
        />
      ) : (
        // Android: Show message and share button
        <View style={styles.androidContainer}>
          <View style={styles.androidContent}>
            <Feather name="file-text" size={64} color="#C14242" />
            <Text style={styles.androidTitle}>PDF Recipe</Text>
            <Text style={styles.androidMessage}>
              Tap the share button above to open this PDF in your preferred PDF viewer app.
            </Text>
            <TouchableOpacity style={styles.openButton} onPress={handleSharePdf}>
              <Text style={styles.openButtonText}>Open PDF</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Loading or Error State */}
      {(isLoading || error) && shouldUseWebView && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>
            {error ? `Error: ${error}` : 'Loading PDF...'}
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff',
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  shareButton: {
    padding: 8,
  },
  webview: {
    flex: 1,
    width: Dimensions.get("window").width,
  },
  androidContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  androidContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  androidTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 16,
  },
  androidMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  openButton: {
    backgroundColor: '#C14242',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  openButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  statusContainer: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  statusText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 10,
    borderRadius: 8,
  },
});
