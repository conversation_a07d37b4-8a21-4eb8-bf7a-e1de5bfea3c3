import React, { useState, useEffect } from "react";
import { StyleSheet, Dimensions, View, TouchableOpacity, Text, Platform } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Feather } from "@expo/vector-icons";
import { WebView } from "react-native-webview";
import * as Sharing from "expo-sharing";

export default function PdfViewer() {
  const { recipeId, recipeName } = useLocalSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [htmlContent, setHtmlContent] = useState(null);

  console.log("PdfViewer received recipe:", recipeId, recipeName);

  const getRecipeContent = (id) => {
    const recipes = {
      cookbook: {
        title: "Healthy Recipe Collection",
        subtitle: "Nutritious recipes for your fitness journey",
        recipes: [
          {
            title: "🥗 Protein Power Bowl",
            ingredients: [
              "2 cups quinoa, cooked",
              "1 grilled chicken breast, sliced",
              "1 cup mixed greens",
              "1/2 avocado, sliced",
              "1/4 cup cherry tomatoes",
              "2 tbsp olive oil",
              "1 tbsp lemon juice",
              "Salt and pepper to taste"
            ],
            instructions: [
              "Cook quinoa according to package instructions and let cool.",
              "Season and grill chicken breast until fully cooked.",
              "Arrange quinoa in a bowl as the base.",
              "Top with mixed greens, sliced chicken, and avocado.",
              "Add cherry tomatoes and drizzle with olive oil and lemon juice.",
              "Season with salt and pepper. Enjoy!"
            ]
          },
          {
            title: "🥤 Post-Workout Smoothie",
            ingredients: [
              "1 banana",
              "1 cup almond milk",
              "1 scoop protein powder (vanilla)",
              "1 tbsp almond butter",
              "1 tsp honey",
              "1/2 cup ice cubes",
              "1 tsp chia seeds (optional)"
            ],
            instructions: [
              "Add all ingredients to a blender.",
              "Blend on high speed for 60-90 seconds until smooth.",
              "Pour into a glass and enjoy immediately.",
              "Sprinkle chia seeds on top if desired."
            ]
          }
        ]
      },
      easy_recipes: {
        title: "Quick & Easy Recipes",
        subtitle: "Simple recipes ready in under 30 minutes",
        recipes: [
          {
            title: "🍳 High-Protein Breakfast",
            ingredients: [
              "3 eggs",
              "1/4 cup egg whites",
              "1/2 cup spinach, chopped",
              "1/4 cup feta cheese",
              "1 slice whole grain toast",
              "1 tbsp olive oil",
              "Salt, pepper, and herbs to taste"
            ],
            instructions: [
              "Heat olive oil in a non-stick pan over medium heat.",
              "Add spinach and cook until wilted.",
              "Beat eggs and egg whites together, season with salt and pepper.",
              "Pour egg mixture into the pan with spinach.",
              "Add feta cheese and fold the omelet in half.",
              "Serve with toasted whole grain bread."
            ]
          },
          {
            title: "🥙 Mediterranean Wrap",
            ingredients: [
              "1 large whole wheat tortilla",
              "1/4 cup hummus",
              "1/2 cup grilled chicken, sliced",
              "1/4 cup cucumber, diced",
              "1/4 cup tomatoes, diced",
              "2 tbsp red onion, sliced",
              "2 tbsp feta cheese",
              "Fresh lettuce leaves"
            ],
            instructions: [
              "Spread hummus evenly on the tortilla.",
              "Layer lettuce, chicken, cucumber, tomatoes, and red onion.",
              "Sprinkle feta cheese on top.",
              "Roll tightly and slice in half to serve."
            ]
          }
        ]
      }
    };

    return recipes[id] || recipes.cookbook;
  };

  useEffect(() => {
    if (recipeId) {
      const recipeData = getRecipeContent(recipeId);
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>PDF Viewer</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background-color: #f5f5f5;
            }
            .container {
              max-width: 800px;
              margin: 0 auto;
              background: white;
              border-radius: 8px;
              padding: 20px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
              padding-bottom: 20px;
              border-bottom: 1px solid #eee;
            }
            .title {
              color: #C14242;
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .subtitle {
              color: #666;
              font-size: 16px;
            }
            .content {
              line-height: 1.6;
              color: #333;
            }
            .recipe-section {
              margin-bottom: 30px;
            }
            .recipe-section h2 {
              color: #C14242;
              border-bottom: 2px solid #C14242;
              padding-bottom: 5px;
              margin-bottom: 15px;
            }
            .ingredients {
              background: #f9f9f9;
              padding: 15px;
              border-radius: 5px;
              margin-bottom: 20px;
            }
            .ingredients ul {
              margin: 0;
              padding-left: 20px;
            }
            .instructions {
              counter-reset: step-counter;
            }
            .instructions ol {
              padding-left: 0;
            }
            .instructions li {
              counter-increment: step-counter;
              margin-bottom: 15px;
              padding-left: 30px;
              position: relative;
            }
            .instructions li::before {
              content: counter(step-counter);
              position: absolute;
              left: 0;
              top: 0;
              background: #C14242;
              color: white;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="title">${recipeData.title}</div>
              <div class="subtitle">${recipeData.subtitle}</div>
            </div>

            <div class="content">
              ${recipeData.recipes.map(recipe => `
                <div class="recipe-section">
                  <h2>${recipe.title}</h2>
                  <div class="ingredients">
                    <h3>Ingredients:</h3>
                    <ul>
                      ${recipe.ingredients.map(ingredient => `<li>${ingredient}</li>`).join('')}
                    </ul>
                  </div>
                  <div class="instructions">
                    <h3>Instructions:</h3>
                    <ol>
                      ${recipe.instructions.map(instruction => `<li>${instruction}</li>`).join('')}
                    </ol>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </body>
        </html>
      `;

      const htmlUri = `data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`;
      setHtmlContent(htmlUri);
      setIsLoading(false);
      console.log("HTML recipe content created for:", recipeId);
    }
  }, [recipeId]);

  const handleSharePdf = async () => {
    try {
      if (await Sharing.isAvailableAsync()) {
        // Create a simple text to share since we don't have a file
        const shareText = `Check out this recipe: ${recipeName || 'Recipe Collection'}`;
        await Sharing.shareAsync(shareText);
      } else {
        alert('Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Error sharing recipe:', error);
      alert('Failed to share recipe');
    }
  };

  if (!recipeId) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Feather name="arrow-left" size={24} color="#C14242" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Recipe</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>No recipe specified</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Feather name="arrow-left" size={24} color="#C14242" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Recipe</Text>
        <TouchableOpacity
          style={styles.shareButton}
          onPress={handleSharePdf}
        >
          <Feather name="share" size={20} color="#C14242" />
        </TouchableOpacity>
      </View>

      {/* Recipe Viewer using WebView with HTML content */}
      {htmlContent && (
        <WebView
          source={{ uri: htmlContent }}
          style={styles.webview}
          onLoadStart={() => {
            console.log("WebView started loading");
            setIsLoading(true);
            setError(null);
          }}
          onLoadEnd={() => {
            console.log("WebView finished loading");
            setIsLoading(false);
          }}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.error('WebView error:', nativeEvent);
            setError('Failed to load recipe content.');
            setIsLoading(false);
          }}
          onHttpError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.error('WebView HTTP error:', nativeEvent);
            setError(`HTTP Error: ${nativeEvent.statusCode}`);
            setIsLoading(false);
          }}
          startInLoadingState={true}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={false}
          renderLoading={() => (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading Recipe...</Text>
            </View>
          )}
        />
      )}

      {/* Loading State */}
      {isLoading && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusText}>Loading Recipe...</Text>
        </View>
      )}

      {/* Error State */}
      {error && (
        <View style={styles.errorContainer}>
          <Feather name="alert-circle" size={48} color="#C14242" />
          <Text style={styles.errorTitle}>Unable to Load Recipe</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => {
            setError(null);
            setIsLoading(true);
            // Force re-render by recreating the content
            const recipeData = getRecipeContent(recipeId);
            const newHtmlContent = htmlContent.replace(/&t=\d+/, '') + `&t=${Date.now()}`;
            setHtmlContent(newHtmlContent);
          }}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.shareButtonAlt} onPress={handleSharePdf}>
            <Text style={styles.shareButtonAltText}>Open in External App</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff',
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  shareButton: {
    padding: 8,
  },
  webview: {
    flex: 1,
    width: Dimensions.get("window").width,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#C14242',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  shareButtonAlt: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#C14242',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  shareButtonAltText: {
    color: '#C14242',
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusContainer: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    margin: 20,
    borderRadius: 8,
    padding: 20,
  },
  statusText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
