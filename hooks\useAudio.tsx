import { useState, useEffect } from 'react';
import { Audio } from 'expo-av';

const useAudio = (soundPath: any) => {
  const [sound, setSound] = useState<Audio.Sound | null>(null);

  useEffect(() => {
    async function loadSound() {
      try {
        const { sound } = await Audio.Sound.createAsync(soundPath);
        setSound(sound);
        // Do not play sound on mount
      } catch (error) {
        console.error('Error loading sound:', error);
      }
    }

    loadSound();

    return () => {
      if (sound && typeof sound.unloadAsync === 'function') {
        sound.unloadAsync();
      }
    };
  }, [soundPath]);

  const playSound = async () => {
    if (sound && typeof sound.replayAsync === 'function') {
      try {
        await sound.replayAsync();
      } catch (error) {
        console.error('Error playing sound:', error);
      }
    }
  };

  return { playSound };
};

export default useAudio;