import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import BASE_URL from '../api/baseUrl';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import ProgramLoadingScreen from './programLoadingScreen';
import useLanguage from '../hooks/useLanguage';

const Programs = () => {
  const [programs, setPrograms] = useState([]);
  const [userName, setUserName] = useState('');
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { t } = useLanguage();

  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        setLoading(true);
        const token = await AsyncStorage.getItem('authToken');
        const userEmail = await AsyncStorage.getItem('userEmail');
        setUserName(userEmail ? userEmail.split('@')[0] : 'User');
        const response = await fetch(`${BASE_URL}/programs`, {
          headers: {
            Accept: 'application/json',
            Authorization: `Bearer ${token}`,
          },
        });
        const data = await response.json();
        setPrograms(Array.isArray(data) ? data : []);
      } catch (e) {
        setPrograms([]);
      } finally {
        setLoading(false);
      }
    };
    fetchPrograms();
  }, []);

  if (loading) {
    return <ProgramLoadingScreen />;
  }

  // Add a refresh handler
  const handleRefresh = () => {
    fetchPrograms();
  };

  return (
    <LinearGradient
      colors={["rgb(245,245,245)", "rgb(255,255,255)"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.safeArea}
    >
      <View style={styles.header}>
        <SafeAreaView style={{width: '100%'}}>
          <Text style={[styles.headerText, { textAlign: 'left' }]}>{t.hello} {userName}</Text>
          <Text style={[styles.subHeaderText, { color: 'white' }]}>{t.select_program}</Text>
        </SafeAreaView>
      </View>
      {Array.isArray(programs) && programs.length === 0 ? (
        <View style={{ alignItems: 'center', marginTop: 32 }}>
          <Text style={{ color: '#C14242', fontSize: 16, marginBottom: 12 }}>{t.no_programs || 'No programs found.'}</Text>
          <TouchableOpacity
            style={{ backgroundColor: '#C14242', borderRadius: 20, paddingVertical: 10, paddingHorizontal: 24 }}
            onPress={handleRefresh}
          >
            <Text style={{ color: '#fff', fontWeight: 'bold' }}>{t.refresh || 'Refresh'}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView contentContainerStyle={styles.programList}>
          {Array.isArray(programs) && programs.map((program) => (
            <TouchableOpacity
              key={program.id}
              style={styles.programCard}
              activeOpacity={0.85}
              onPress={() => router.replace({ pathname: '/(tabs)/home', params: { program: JSON.stringify(program) } })}
            >
              {program.imageUrl ? (
                <Image source={{ uri: program.imageUrl }} style={styles.programImage} />
              ) : (
                <View style={[styles.programImage, { backgroundColor: '#eee', justifyContent: 'center', alignItems: 'center' }]}>
                  <Text style={{ color: '#aaa' }}>No Image</Text>
                </View>
              )}
              <View style={styles.programInfo}>
                <Text style={styles.programName}>{program.name}</Text>
                <Text style={styles.programDescription}>{program.description}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      )}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F0F4FF',
  },
  header: {
    backgroundColor: '#C14242',
    paddingTop: 40,
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    alignItems: 'center',
    shadowColor: '#C14242',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  },
  headerText: {
    color: 'white',
    fontSize: 26,
    fontFamily: 'DancingScript-VariableFont_wght',
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 8,
    textAlign: 'left',
  },
  subHeaderText: {
    color: '#fff',
    fontSize: 12,
    opacity: 1,
    marginBottom: 8,
    textAlign: 'left',
  },
  programList: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 8,
  },
  programCard: {
    width: '98%',
    maxWidth: 370,
    marginBottom: 24,
    borderRadius: 18,
    backgroundColor: 'rgba(255,255,255,0.9)',
    overflow: 'hidden',
    shadowColor: '#C14242',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.13,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: '#f3e6ef',
  },
  programImage: {
    width: '100%',
    height: 160,
    resizeMode: 'cover',
    backgroundColor: '#f5f5f5',
  },
  programInfo: {
    padding: 18,
    backgroundColor: '#fff',
  },
  programName: {
    fontSize: 16,
    color: '#C14242',
    fontWeight: 'bold',
    textAlign: 'left',
    marginBottom: 6,
  },
  programDescription: {
    fontSize: 12,
    color: 'rgb(107,114,128)',
    marginTop: 2,
    textAlign: 'left',
  },
});

export default Programs; 