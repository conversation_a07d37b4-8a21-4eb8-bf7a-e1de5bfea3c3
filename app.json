{"expo": {"name": "BodyByXhes", "slug": "BodyByXhes", "version": "1.0.0", "orientation": "portrait", "newArchEnabled": true, "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff", "package": "com.mycompany.myapp"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-asset"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "75aad477-0ed1-4f9d-ae27-ca26ba21800b"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/75aad477-0ed1-4f9d-ae27-ca26ba21800b"}}}