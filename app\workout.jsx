import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  Keyboard,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useLocalSearchParams } from 'expo-router/build/hooks';

// Import custom hooks
import useTimeSpent from './withTimeSpent';
import { incrementWorkoutCount } from './workoutStorage';
import useLastExercise from '../app/useLastExercise';
import programsApi from '../api/programsApi';
import useTimer from '../hooks/useTimer';
import useAudio from '../hooks/useAudio';
import AsyncStorage from '@react-native-async-storage/async-storage';
import useLanguage from '../hooks/useLanguage';

// Import components
import VideoPlayer from '../components/VideoPlayer';
import ExerciseHeader from '../components/ExerciseHeader';
import SectionHighlight from '../components/SectionHighlight';
import ExerciseList from '../components/ExerciseList';
import ActionButton from '../components/ActionButton';
import ProgramLoadingScreen from './programLoadingScreen';
import { LinearGradient } from 'expo-linear-gradient';

const VISIBLE_COUNT = 4;
const { width, height } = Dimensions.get('window');

const WorkoutView = () => {
  console.log('WorkoutView rendered');
  const params = useLocalSearchParams();
  const { workout: workoutParam } = params;
  const router = useRouter();
  const { t, language } = useLanguage();

  // Parse workout data from params
  const workoutData = useMemo(() => workoutParam ? JSON.parse(workoutParam) : null, [workoutParam]);

  // Local state
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [focusedInputIndex, setFocusedInputIndex] = useState(null);
  const [completedExercises, setCompletedExercises] = useState([]);
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [exerciseWeights, setExerciseWeights] = useState({});
  // Timer duration state (rest time for current exercise)
  const [timerDuration, setTimerDuration] = useState(30);

  // Audio for timer
  const { playSound: rawPlaySound } = useAudio(require('../assets/countdown.wav'));
  const playSound = useCallback(() => rawPlaySound(), [rawPlaySound]);

  // Timer hook (use timerDuration as initialTime)
  const { timer, isTimerActive, toggleTimer, resetTimer } = useTimer(
    timerDuration,
    null,
    undefined,
    playSound
  );

  // Timeout ref for debounced saving
  const saveTimeoutRef = useRef(null);

  // Helper function to get storage key
  const getStorageKey = () => `exerciseWeights_${workoutData?.id || workoutData?.name || 'workout'}_v2`;

  // Helper function to get consistent exercise ID
  const getExerciseId = (exercise, index) => {
    return exercise.id || exercise.exerciseId || `exercise_${exercise.name || exercise.exercise || index}`;
  };

  // Use exercises from workoutData
  const exercises = workoutData && workoutData.exercises ? workoutData.exercises : [];

  // Update timer duration when current exercise changes
  useEffect(() => {
    if (exercises.length > 0) {
      // Prefer restBetweenSets if present, otherwise fallback to rest, otherwise 30
      const rest =
        typeof exercises[currentExerciseIndex]?.restBetweenSets === 'number'
          ? exercises[currentExerciseIndex].restBetweenSets
          : (typeof exercises[currentExerciseIndex]?.rest === 'number'
              ? exercises[currentExerciseIndex].rest
              : 30);
      setTimerDuration(rest);
    }
  }, [currentExerciseIndex, exercises]);

  // Reset timer to new duration when timerDuration changes (and not first mount)
  useEffect(() => {
    resetTimer();
  }, [timerDuration]);

  // Load saved weights when component mounts
  useEffect(() => {
    const loadSavedWeights = async () => {
      try {
        const storageKey = getStorageKey();
        const savedWeights = await AsyncStorage.getItem(storageKey);
        
        if (savedWeights) {
          const parsedWeights = JSON.parse(savedWeights);
          console.log('Loaded saved weights:', parsedWeights);
          setExerciseWeights(parsedWeights);
        } else {
          console.log('No saved weights found');
        }
      } catch (error) {
        console.error('Error loading saved weights:', error);
      }
    };
    
    if (workoutData) {
      loadSavedWeights();
    }
  }, [workoutData]);

  // Save weights with debouncing whenever exerciseWeights changes
  useEffect(() => {
    const saveWeights = async () => {
      try {
        // Clear existing timeout
        if (saveTimeoutRef.current) {
          clearTimeout(saveTimeoutRef.current);
        }
        
        // Debounce saving to avoid too frequent writes
        saveTimeoutRef.current = setTimeout(async () => {
          // Convert string values to numbers for storage, but keep empty strings as 0
          const weightsToSave = {};
          Object.keys(exerciseWeights).forEach(key => {
            const value = exerciseWeights[key];
            weightsToSave[key] = value === '' ? 0 : (parseFloat(value) || 0);
          });
          
          const storageKey = getStorageKey();
          await AsyncStorage.setItem(storageKey, JSON.stringify(weightsToSave));
          console.log('Weights saved:', weightsToSave);
        }, 500); // Wait 500ms after last change
        
      } catch (error) {
        console.error('Error saving weights:', error);
      }
    };
    
    if (Object.keys(exerciseWeights).length > 0) {
      saveWeights();
    }

    // Cleanup timeout on unmount
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [exerciseWeights]);

  // Increment workout count
  useEffect(() => {
    incrementWorkoutCount();
  }, []);

  if (!workoutData) {
    return <ProgramLoadingScreen />;
  }
  if (!exercises.length) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={[styles.container, styles.loadingContainer]}>
          <Text style={styles.errorText}>No exercises found for this workout</Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentExercise = exercises[currentExerciseIndex] || {};
  const currentVideoUri = currentExercise.videoUrl || '';
  // Get instructions for the current language, mapping 'al' to 'sq' for Albanian
  const instructionsLangKey = language === 'al' ? 'sq' : language;
  const instructionsArr = currentExercise.instructions && currentExercise.instructions[instructionsLangKey];
  const visibleExercises = exercises.slice(currentExerciseIndex, currentExerciseIndex + 4);

  // Improved handleWeightChange function
  const handleWeightChange = (exerciseId, value) => {
    // Allow empty string, only convert to number when saving or displaying
    // This allows users to delete all text and start fresh
    setExerciseWeights(prev => ({
      ...prev,
      [exerciseId]: value // Store the actual string value
    }));
    
    console.log(`Weight updated for ${exerciseId}: ${value}`);
  };

  // Create weights array with saved values
  const getWeightsArray = () => {
    return exercises.map((exercise, index) => {
      const exerciseId = getExerciseId(exercise, index);
      const savedWeight = exerciseWeights[exerciseId];
      
      // If we have a saved weight, use it (could be string or number)
      if (savedWeight !== undefined && savedWeight !== null) {
        return {
          exerciseId,
          weight: savedWeight.toString() // Convert to string for input display
        };
      }
      
      // Fallback to exercise default weight
      return {
        exerciseId,
        weight: (exercise.weight || 0).toString()
      };
    });
  };

  const handleNext = () => {
    if (currentExerciseIndex < exercises.length - 1) {
      setCurrentExerciseIndex((prevIndex) => prevIndex + 1);
      playSound();
    }
  };

  const handleDone = () => {
    const currentExercise = exercises[currentExerciseIndex];
    
    if (currentExerciseIndex === exercises.length - 1) {
      router.push('/(tabs)/home');
    } else {
      setCompletedExercises(prev => [...prev, currentExerciseIndex]);
      setCurrentExerciseIndex(prevIndex => prevIndex + 1);
    }
  };

  const goToNextVideo = () => {
    if (currentVideoIndex < exercises.length - 1) {
      setCurrentVideoIndex(currentVideoIndex + 1);
    } else {
      setCurrentVideoIndex(0);
    }
  };

  const handleInputFocus = (index) => {
    setFocusedInputIndex(index);
  };

  const handleInputBlur = () => {
    setFocusedInputIndex(null);
  };

  const toggleDescription = () => {
    setIsDescriptionExpanded(!isDescriptionExpanded);
  };

  const handleActionButton = () => {
    Keyboard.dismiss();
    if (currentExerciseIndex < exercises.length - 1) {
      setCurrentExerciseIndex(prev => prev + 1);
      setCurrentVideoIndex(prev => prev + 1);
    } else {
      router.push('/(tabs)/home');
    }
  };

  // Debug function (can be called from console if needed)
  const debugWeights = () => {
    console.log('Current exerciseWeights state:', exerciseWeights);
    console.log('Exercises with IDs:', exercises.map(ex => ({ 
      name: ex.name, 
      exerciseId: getExerciseId(ex, exercises.indexOf(ex))
    })));
    console.log('Weights array:', getWeightsArray());
  };

  return (
    <View style={styles.fullScreenContainer}>

<LinearGradient
        colors={["rgb(255, 105, 180)", "#C14242"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View  style={[styles.header]} >
          <View style={{ flex: 1 }}>
            <SafeAreaView>
            <Text style={{ fontFamily: 'DancingScript-VariableFont_wght', fontWeight: 'bold', fontSize: 22, color: '#fff', textAlign: 'left' }}></Text>
            </SafeAreaView>
          </View>
        </View>
      </LinearGradient>
      {/* Video Player without SafeAreaView - takes full width */}
      <View style={styles.videoContainer}>
        <VideoPlayer videoUri={currentVideoUri} />
      </View>

      {/* Main content with SafeAreaView */}
        <View style={styles.container}>
          <View>
            <ExerciseHeader
              exerciseName={currentExercise.name || currentExercise.exercise || 'Exercise'}
              description={currentExercise.description || currentExercise.workoutDescription || ''}
              isDescriptionExpanded={isDescriptionExpanded}
              onToggleDescription={toggleDescription}
              timer={timer}
              isTimerActive={isTimerActive}
              onToggleTimer={() => {
                if (isTimerActive) {
                  toggleTimer(); // Pause if running
                } else {
                  resetTimer(); // Reset to rest time
                  toggleTimer(); // Start
                }
              }}
              sets={currentExercise.sets}
              reps={currentExercise.reps}
              restBetweenSets={typeof currentExercise.restBetweenSets === 'number' ? currentExercise.restBetweenSets : (typeof currentExercise.rest === 'number' ? currentExercise.rest : 0)}
            />
          </View>

          {/* Show instructions in the selected language */}
          {Array.isArray(instructionsArr) && instructionsArr.length > 0 && (
            <View style={{ marginBottom: 16 }}>
              <Text style={{ fontWeight: '500', fontSize: 16, marginBottom: 4 }}>{t.instructions || 'Instructions'}:</Text>
              {instructionsArr.map((line, idx) => (
                <Text key={idx} style={{ fontSize: 14, color: '#333', marginBottom: 2 }}>{line}</Text>
              ))}
            </View>
          )}

          <SectionHighlight title={t.workouts_done} />

          <ExerciseList
            exercises={exercises}
            weights={getWeightsArray()}
            focusedInputIndex={focusedInputIndex}
            currentExerciseIndex={currentExerciseIndex}
            onWeightChange={handleWeightChange}
            onInputFocus={handleInputFocus}
            onInputBlur={handleInputBlur}
          />
        </View>

        <ActionButton
          title={currentExerciseIndex === exercises.length - 1 ? t.done : t.next}
          onPress={handleActionButton}
        />
    </View>
  );
};

const styles = StyleSheet.create({
  fullScreenContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    backgroundColor: '#C14242',
    paddingBottom: 16,
    paddingHorizontal: 20,
    alignItems: 'center',
    shadowColor: '#C14242',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: 0,
    paddingBottom: 0,
  },
  videoContainer: {
    // Remove any padding/margins that might affect video display
    width: '100%',
    // Let VideoPlayer component handle its own height
  },
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 12, // Reduced from 16 to make content more compact
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF5F7',
  },
  loadingTitle: {
    fontSize: 32,
    fontFamily: 'Playfair Display',
    color: '#C14242',
    textAlign: 'center',
    letterSpacing: 1,
  },
  errorText: {
    fontSize: 18,
    color: '#C14242',
    textAlign: 'center',
    marginBottom: 10,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginBottom: 5,
  },
  floatingElements: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  floatingEmoji: {
    position: 'absolute',
    fontSize: 24,
    opacity: 0.6,
  },
  headerContainer: {
    width: '100%',
    alignItems: 'flex-start',
    marginVertical: 6, // Reduced from 8
    paddingHorizontal: 12, // Reduced from 16
  },
  exerciseTitle: {
    fontSize: 22, // Reduced from 24
    fontWeight: 'bold',
    marginBottom: 6, // Reduced from 8
    color: '#1F2937',
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6, // Reduced from 8
  },
  durationText: {
    fontSize: 15, // Reduced from 16
    color: '#9CA3AF',
    marginLeft: 6, // Reduced from 8
    fontWeight: '500',
  },
  description: {
    fontSize: 15, // Reduced from 16
    color: '#6B7280',
    marginBottom: 6, // Reduced from 8
  },
  weightInput: {
    width: 45, // Reduced from 50
    height: 28, // Reduced from 32
    borderWidth: 0,
    borderRadius: 5, // Reduced from 6
    paddingHorizontal: 3, // Reduced from 4
    textAlign: 'center',
    textAlignVertical: 'center',
    fontSize: 14, // Reduced from 15
    lineHeight: 28, // matches height for vertical centering
    backgroundColor: '#fff',
  },
});

export default WorkoutView;