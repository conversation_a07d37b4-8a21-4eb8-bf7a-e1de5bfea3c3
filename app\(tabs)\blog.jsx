import { View, Text, StyleSheet, ScrollView, Image, TouchableOpacity, SafeAreaView } from 'react-native'
import React from 'react'
import { LinearGradient } from 'expo-linear-gradient';

const blog = () => {
  return (

    <ScrollView style={styles.scrollContainer}>
      <LinearGradient
        colors={['#E08080', '#C14242']} // Light brick red to brick red
        style={styles.container}
      >
        <Text style={styles.text}>Blog</Text>
      </LinearGradient>
      
      <View style={styles.content}>
        {/* Header with profile info */}
        <View style={styles.headerContainer}>
          <Image
            source={{ uri: 'https://avatar.iran.liara.run/public/88' }}
            style={styles.profileImage}
            resizeMode="cover"
          />
          <View style={styles.userInfo}>
            <View style={styles.nameContainer}>
              <Text style={styles.displayName}>Profile Name</Text>
              <Image 
                source={require('../../assets/icons/check.png')} 
                style={styles.verifiedIcon} 
              />
            </View>
            <Text style={styles.username}>@username</Text>
            <Text style={styles.timestamp}>04 December 2021</Text>
          </View>
        </View>

        {/* Post content */}
        <View style={styles.postContent}>
          <Text style={styles.postText}>
            Today I wanted to share with you what I had thought would be very important on the upcoming days starting from now. I hope you have a great week. Remember, as long as you're doing it, it always counts.
          </Text>
        </View>

        {/* Separator line */}
        <View style={styles.separator} />

        {/* Action buttons with stats */}
        <View style={styles.actionContainer}>
          <TouchableOpacity style={styles.actionButton}>
            <Image source={require('../../assets/icons/coment.png')} style={styles.actionIcon} />
            <Text style={styles.statText}>568k</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Image source={require('../../assets/icons/enter.png')} style={styles.actionIcon} />
            <Text style={styles.statText}>75k</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Image source={require('../../assets/icons/heart.png')} style={styles.actionIcon} />
            <Text style={styles.statText}>157</Text>
          </TouchableOpacity>
        </View>
      </View>

     <View style={styles.content}>
     <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between',}}>
      <Image
        source={{ uri: 'https://avatar.iran.liara.run/public/88' }}
        style={styles.profileImage}
        resizeMode="cover"
      />
     </View>
      <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between',}}>
        <View style={{flexDirection: 'column', alignItems: 'flex-start',}}>
          <Text style={styles.displayName}>Profile Name</Text>
          <Text   style={styles.username}>@username</Text>
          <Text style={styles.timestamp}>04 December 2021</Text>
          <Image source={require('../../assets/icons/check.png')} style={styles.verifiedIcon} />
        </View>
      </View>
        <View style={styles.postContent}>
          <Text style={styles.postText}>
            Today I wanted to share with you what I had thought would be very important on the upcoming days starting from now. I hope you have a great week. Remember, as long as you're doing it, it always counts.
          </Text>
        </View>

        <View style={styles.separator} />

        <View style={styles.actionContainer}>
          <TouchableOpacity style={styles.actionButton}>
            <Image source={require('../../assets/icons/coment.png')} style={styles.actionIcon} />
            <Text style={styles.statText}>568k</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Image source={require('../../assets/icons/enter.png')} style={styles.actionIcon} />
            <Text style={styles.statText}>75k</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Image source={require('../../assets/icons/heart.png')} style={styles.actionIcon} />
            <Text style={styles.statText}>157</Text>
          </TouchableOpacity>
        </View>
     </View>
    </ScrollView>
 
  )
}

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  container: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  text: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    backgroundColor: '#fff',
    borderRadius: 12,
    margin: 10,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  profileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  displayName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginRight: 4,
  },
  verifiedIcon: {
    width: 16,
    height: 16,
  },
  username: {
    fontSize: 14,
    color: '#657786',
    marginBottom: 2,
  },
  timestamp: {
    fontSize: 12,
    color: '#657786',
  },
  postContent: {
    marginBottom: 16,
  },
  postText: {
    fontSize: 15,
    lineHeight: 20,
    color: '#14171a',
  },
  separator: {
    height: 1,
    backgroundColor: '#e1e8ed',
    marginBottom: 8,
  },
  actionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 20,
  },
  actionIcon: {
    width: 20,
    height: 20,
    tintColor: '#657786',
    marginRight: 6,
  },
  statText: {
    fontSize: 13,
    color: '#657786',
    fontWeight: '500',
  },
});

export default blog