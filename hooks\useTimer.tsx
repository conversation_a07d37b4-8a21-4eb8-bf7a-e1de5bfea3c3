import { useState, useEffect } from 'react';

const useTimer = (
  initialTime: number = 30,
  onTimerEnd?: () => void,
  incrementTimeSpent?: () => void,
  playSound?: () => void
) => {
  const [timer, setTimer] = useState(initialTime);
  const [isTimerActive, setIsTimerActive] = useState(false);

  // Update timer if initialTime changes and timer is not running
  useEffect(() => {
    if (!isTimerActive) {
      setTimer(initialTime);
    }
    // If you want to always update timer even if running, remove the isTimerActive check
  }, [initialTime]);

  useEffect(() => {
    let interval: any;
    if (isTimerActive && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => {
          if (prev === 4 && playSound) {
            playSound(); // Play sound when 3 seconds left (prev is 4, next is 3)
          }
          return Math.max(prev - 1, 0);
        });
        if (incrementTimeSpent) {
          incrementTimeSpent();
        }
      }, 1000);
    } else if (timer === 0) {
      setIsTimerActive(false);
      if (onTimerEnd) {
        onTimerEnd();
      }
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTimerActive, timer, incrementTimeSpent, playSound, onTimerEnd]);

  const toggleTimer = () => {
    setIsTimerActive((active) => !active);
  };

  const resetTimer = () => {
    setTimer(initialTime);
    setIsTimerActive(false);
  };

  return {
    timer,
    isTimerActive,
    toggleTimer,
    resetTimer,
  };
};

export default useTimer;