import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import translations from '../constants/i18n';

const LANGUAGE_KEY = 'appLanguage';

export default function useLanguage() {
  const [language, setLanguageState] = useState('en');
  const [t, setT] = useState(translations['en']);

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const storedLang = await AsyncStorage.getItem(LANGUAGE_KEY);
        if (storedLang && translations[storedLang]) {
          setLanguageState(storedLang);
          setT(translations[storedLang]);
        }
      } catch {}
    };
    loadLanguage();
  }, []);

  const setLanguage = useCallback(async (lang) => {
    if (translations[lang]) {
      setLanguageState(lang);
      setT(translations[lang]);
      await AsyncStorage.setItem(LANGUAGE_KEY, lang);
    }
  }, []);

  return { language, t, setLanguage };
} 