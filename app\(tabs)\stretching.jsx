import { View, Text, StyleSheet, ScrollView } from 'react-native';
import React from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { Image } from 'react-native';
import { TouchableOpacity } from 'react-native';

const Stretching = () => {
  return (
     <ScrollView>
      <LinearGradient
        colors={['#E08080', '#C14242']} // Light brick red to brick red
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{ borderBottomLeftRadius: 24, borderBottomRightRadius: 24 }}
      >
        <View style={styles.header}>
          <View style={{ flex: 1 }}>
            <Text style={[styles.headerTitle, { color: '#fff', fontSize: 16, fontWeight: 'bold' }]}>
              Stretching
            </Text>
          </View>
          </View>
          </LinearGradient>
           <View style={styles.content}>
            <View style={{  flexDirection: 'row',justifyContent: 'space-between',alignItems: 'center',marginBottom: 20}}>
          <Text style={{  marginLeft: 15,marginTop: 15, color: '#0e0d0dff', fontSize: 28, fontWeight: 'bold', }}>Stretching</Text>
          
          <View style={{   marginTop: 15,marginRight: 15,backgroundColor: 'rgba(193, 66, 66, 0.1)',paddingHorizontal: 12,paddingVertical: 6,borderRadius: 20,borderWidth: 1,borderColor: 'rgba(193, 66, 66, 0.3)', }}>
          <Text style={{   color: '#C14242',fontSize: 12,fontWeight: 'bold',}}>Pose 1</Text>
          </View>
          </View>
          {/* Wrap image and overlay in a container */}
          <View style={styles.imageContainer}>
             <Image
              source={{ uri: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80' }}
              style={styles.stretchingImage}
              resizeMode="cover"
            />
            <LinearGradient
              colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.3)']}
              style={styles.imageOverlay}
            />
            <View style={styles.textOverlay}>
              <Text style={styles.overlayTitle}>Mountain Pose</Text>
              <Text style={styles.overlaySubtitle}>5 min • Beginner</Text>
            </View>
          </View>
            <View style={{  flexDirection: 'row',justifyContent: 'space-around',marginBottom: 20, marginTop: 10, }}>
                <View style={{    backgroundColor: '#f8f9fa',padding: 15,borderRadius: 12,alignItems: 'center',flex: 1, marginHorizontal: 5, }}>
                    <Text style={{ fontSize: 24,fontWeight: 'bold',color: '#333',marginBottom: 4,}}>5</Text>
                    <Text style={{ fontSize: 12,fontWeight: 600, color: '#777575ff',}}>min</Text>
                </View>
                <View style={{    backgroundColor: '#f8f9fa',padding: 15,borderRadius: 12,alignItems: 'center',flex: 1,marginHorizontal: 5, }}>
                    <Text style={{ fontSize: 24,fontWeight: 'bold',color: '#333',marginBottom: 4,}}>💪</Text>
                    <Text style={{ fontSize: 12,fontWeight: 600, color: '#777575ff',}}>Beginner</Text>
                </View>
                <View style={{    backgroundColor: '#f8f9fa',padding: 15,borderRadius: 12,alignItems: 'center',flex: 1,marginHorizontal: 5, }}>
                    <Text style={{ fontSize: 24, fontWeight: 'bold',color: '#333',marginBottom: 4,}}>🔥</Text>
                    <Text style={{ fontSize: 12,fontWeight: 600, color: '#777575ff',}}>Relaxing</Text>
                </View>
            </View>
            <View style={{marginBottom: 20}}>
                <Text style={{  fontSize: 18,fontWeight: 'bold',color: '#0e0d0dff',marginBottom: 8,marginLeft: 10,}}>Description</Text>
                <Text style={{  fontSize: 14,color: '#777575ff',lineHeight: 20,marginLeft: 10,}}>This gentle stretching pose helps improve posture, reduces tension in the spine, and promotes relaxation throughout the body.</Text>
            </View>
                 <TouchableOpacity style={styles.startButton}>
            <LinearGradient
              colors={['#E08080', '#C14242']} // Updated to match header gradient
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.buttonGradient}
            >
              <Text style={styles.buttonText}>Start Stretching</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
     </ScrollView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: '#C14242',
    height: 120,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontFamily: 'DancingScript-VariableFont_wght',
    fontWeight: 'bold',
    fontSize: 26,
    color: '#fff',
    textAlign: 'center',
    letterSpacing: 1,
  },
  content: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 24,
    marginTop: 24,
    marginBottom: 60,
    marginLeft: 10,
    marginRight: 10,
    height: 580,
    width: '95%',
       shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  imageContainer: {
    position: 'relative',
    width: '95%',
    alignSelf: 'center',
  },
  stretchingImage: {
    width: '100%',
    height: 200,
    borderRadius: 24,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  textOverlay: {
    position: 'absolute',
    bottom: 15,
    left: 15,
  },
  overlayTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  overlaySubtitle: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
  },
 description: {
    fontSize: 14,
    color: '#777575ff',
    lineHeight: 20,
  },
  startButton: {
    borderRadius: 25,
    overflow: 'hidden',
    marginBottom: 10,
    width: '96%',
    alignSelf: 'center',
    marginTop: 10,
  },
  buttonGradient: {
    paddingVertical: 15,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default Stretching;