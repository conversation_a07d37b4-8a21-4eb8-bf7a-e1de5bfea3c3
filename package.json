{"name": "bodybyx<PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.0.0", "animejs": "^4.0.2", "axios": "^1.7.9", "expo": "53.0.22", "expo-av": "~15.1.7", "expo-constants": "~17.1.6", "expo-file-viewer": "^0.1.0", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.6", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.11", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-animatable": "^1.4.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.17.4", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-video": "^2.3.1", "react-native-video-player": "^0.14.0", "react-native-web": "^0.20.0", "scheduler": "^0.23.2", "expo-updates": "~0.28.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^18.0.7", "jest": "~29.7.0", "typescript": "~5.8.3"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}