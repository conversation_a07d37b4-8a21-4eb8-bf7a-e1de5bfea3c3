import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, SafeAreaView, Platform } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import ProgramLoadingScreen from '../programLoadingScreen';
import useLanguage from '../../hooks/useLanguage';

const Home = () => {
  const router = useRouter();
  const { program: programParam } = useLocalSearchParams();
  const program = programParam ? JSON.parse(programParam) : null;
  const [selectedWeekIndex, setSelectedWeekIndex] = useState(0);
  const [variation, setVariation] = useState('home');
  const { t } = useLanguage();

  useEffect(() => {
    if (!program) {
      router.replace('/programs');
    }
  }, [program, router]);

  if (!program) {
    return <ProgramLoadingScreen />;
  }

  // Filter weeks by selected variation and sort by week number (order)
  const weeks = (program.weeks || [])
    .filter(w => w.variation === variation)
    .sort((a, b) => (a.order || 0) - (b.order || 0));
  const selectedWeek = weeks[selectedWeekIndex] || {};
  const workouts = selectedWeek.workouts || [];

  return (
    <LinearGradient
      colors={["rgb(245,245,245)", "rgb(255,255,255)"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={{ flex: 1 }}
    >
      <LinearGradient
        colors={['#E08080', '#C14242']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{    borderBottomLeftRadius: 24,
          borderBottomRightRadius: 24,}}
      >
      <SafeAreaView>
      <View style={[styles.header]}>
          <View style={{ flexDirection: 'row', alignItems: 'center', width: '100%', marginTop: 16 }}>
            <TouchableOpacity onPress={() => router.replace('/programs')} style={{ paddingRight: 12, paddingVertical: 4 }}>
              <Ionicons name="arrow-back" size={16} color="#fff" />
            </TouchableOpacity>
            <Text style={[styles.headerText, { textAlign: 'left', flex: 1 }]}>{program.name}</Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 12 }}>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={[styles.weekScroll, { flex: 1 }]}
              contentContainerStyle={{ alignItems: 'center' }}
            >
              {weeks.length === 0 ? (
                <Text style={{ color: '#fff', marginTop: 16 }}>No weeks available for this program.</Text>
              ) : weeks.map((week, idx) => (
                <TouchableOpacity
                  key={week.id}
                  style={[
                    styles.weekButton,
                    idx === selectedWeekIndex ? styles.selectedWeekButton : styles.unselectedWeekButton,
                  ]}
                  onPress={() => setSelectedWeekIndex(idx)}
                >
                  <Text style={[ 
                    styles.weekButtonText,
                    idx === selectedWeekIndex ? { color: '#C14242' } : styles.unselectedWeekButtonText,
                  ]}>{week.order || idx + 1}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
            <TouchableOpacity
              style={[
                styles.toggleButton,
                variation === 'home' ? styles.toggleHome : styles.toggleGym,
                { marginRight: 16, marginLeft: 8 },
              ]}
              onPress={() => {
                setVariation(variation === 'home' ? 'gym' : 'home');
                setSelectedWeekIndex(0);
              }}
            >
              {variation === 'home' ? (
                <Ionicons name="home" size={22} color="#C14242" />
              ) : (
                <Ionicons name="barbell" size={22} color="#C14242" />
              )}
            </TouchableOpacity>
          </View>
        </View>
        </SafeAreaView>
        </LinearGradient>
        <ScrollView contentContainerStyle={styles.workoutList}>
          {workouts.length === 0 ? (
            <Text style={{ color: '#C14242', marginTop: 32, textAlign: 'center' }}>No workouts for this week.</Text>
          ) : workouts.map((workout) => (
            <TouchableOpacity
              key={workout.id}
              style={styles.workoutCard}
              onPress={() => router.push({ pathname: '/workout', params: { id: workout.id, workout: JSON.stringify(workout) } })}
            >
              <Text style={styles.workoutTitle}>{workout.name}</Text>
              <Text style={styles.workoutDescription}>{workout.description}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </LinearGradient>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: '#C14242',
    paddingTop: 0,
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    alignItems: 'center',
    shadowColor: '#E08080',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerText: {
    color: 'white',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 8,
    textAlign: 'left',
    fontFamily: 'DancingScript-VariableFont_wght',
  },
  weekScroll: {
    marginTop: 12,
    marginBottom: 8,
  },
  weekButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#C14242',
    backgroundColor: '#C14242',
  },
  selectedWeekButton: {
    backgroundColor: 'rgb(245,245,245)',
    borderColor: 'rgb(245,245,245)',
    color: '#C14242',
  },
  unselectedWeekButton: {
    backgroundColor: '#C14242',
    borderColor: 'rgb(245,245,245)',
    borderWidth: 1.5,
  },
  weekButtonText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  selectedWeekButtonText: {
    color: '#fff',
  },
  unselectedWeekButtonText: {
    color: '#fff',
    opacity: 0.85,
  },
  workoutList: {
    padding: 20,
  },
  workoutCard: {
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 14,
    padding: 18,
    marginBottom: 16,
    shadowColor: '#C14242',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  workoutTitle: {
    fontSize: 16,
    color: '#C14242',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  workoutDescription: {
    fontSize: 12,
    color: 'rgb(107,114,128)',
  },
  // Add button style
  button: {
    backgroundColor: '#C14242',
    borderRadius: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginTop: 12,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  toggleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginLeft: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#C14242',
    backgroundColor: '#C14242',
  },
  toggleHome: {
    backgroundColor: 'rgb(245,245,245)',
    borderColor: 'rgb(245,245,245)',
  },
  toggleGym: {
    backgroundColor: 'rgb(245,245,245)',
    borderColor: 'rgb(245,245,245)',
    color: '#C14242',
  },
  toggleButtonText: {
    fontWeight: 'bold',
    fontSize: 13,
  },
  selectedToggleText: {
    color: '#fff',
  },
  unselectedToggleText: {
    color: '#fff',
    opacity: 0.85,
  },
});

export default Home;