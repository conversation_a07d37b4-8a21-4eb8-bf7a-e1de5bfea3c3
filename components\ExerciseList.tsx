import React, { useEffect, useRef } from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';

/* -------------------------------------------------------------------------- */
/*                               Type Definitions                              */
/* -------------------------------------------------------------------------- */

interface Exercise {
  id: string;
  exerciseId?: string;
  name_al: string;
  name_en: string;
  description_al: string;
  description_en: string;
  video_url: string;
  image_url: string;
  sets: number;
  reps: number;
  rest_between_sets: number;
  weight?: number;
  instructions: {
    sq: string[];
    en: string[];
  };
  name?: string;
}

interface ExerciseListProps {
  exercises: Exercise[];
  weights: { exerciseId: string; weight: number }[];
  focusedInputIndex: number | null;
  currentExerciseIndex: number;
  onWeightChange: (exerciseId: string, value: string) => void;
  onInputFocus: (index: number) => void;
  onInputBlur: () => void;
}

/* -------------------------------------------------------------------------- */
/*                             Merged Exercise List                            */
/* -------------------------------------------------------------------------- */

const ExerciseList = ({
  exercises,
  weights,
  focusedInputIndex,
  currentExerciseIndex,
  onWeightChange,
  onInputFocus,
  onInputBlur,
}: ExerciseListProps) => {
  /* ------------------------------- Refs ------------------------------------ */
  const scrollViewRef = useRef<ScrollView>(null);
  const inputRefs = useRef<(TextInput | null)[]>([]);

  /* ----------------------------- Helper Fns -------------------------------- */
  const getExerciseId = (exercise: Exercise, index: number) =>
    exercise.exerciseId || exercise.id || `exercise_${exercise.name_al}_${index}`;

  const getWeightForExercise = (exercise: Exercise, index: number) => {
    const id = getExerciseId(exercise, index);
    const found = weights.find((w) => w.exerciseId === id);
    return found?.weight?.toString() ?? exercise.weight?.toString() ?? '';
  };

  /* ---------------------------- Auto‑scroll -------------------------------- */
  useEffect(() => {
    if (scrollViewRef.current && currentExerciseIndex >= 0) {
      const ITEM_HEIGHT = 72; // taller rows for easier tapping
      scrollViewRef.current.scrollTo({ y: currentExerciseIndex * ITEM_HEIGHT, animated: true });
    }
  }, [currentExerciseIndex]);

  /* ------------------------------------------------------------------------ */
  /*                                  Render                                  */
  /* ------------------------------------------------------------------------ */

  return (
    <ScrollView
      ref={scrollViewRef}
      style={styles.scrollView}
      contentContainerStyle={styles.scrollContainer}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      {exercises.map((exercise, index) => {
        const exerciseId = getExerciseId(exercise, index);
        const weight = getWeightForExercise(exercise, index);
        const isHighlighted = index === currentExerciseIndex;
        const isFocused = index === focusedInputIndex;

        /* ------------------------ Per‑row handlers ------------------------- */
        const handleFocus = () => {
          const ref = inputRefs.current[index];
          if (ref && weight.length) {
            // @ts-ignore
            ref.setNativeProps({ selection: { start: 0, end: weight.length } });
          }
          onInputFocus(index);
        };

        const handleBlur = () => onInputBlur();
        const handleChange = (txt: string) => onWeightChange(exerciseId, txt.replace(/[^0-9.]/g, ''));

        /* ----------------------------- UI Row ------------------------------ */
        return (
          <View key={exerciseId} style={[styles.row, isHighlighted && styles.rowHighlighted]}>
            {/* LEFT ‑ icon + single‑line name */}
            <View style={styles.leftSide}>
              {isHighlighted && (
                <View style={styles.iconWrap}>
                  <TouchableOpacity style={styles.iconBtn}>
                    <FontAwesome5 name="play" size={10} color="#4B5563" />
                  </TouchableOpacity>
                </View>
              )}
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[styles.nameText, isHighlighted && styles.nameTextBold]}
              >
                {typeof exercise.name === 'string'
                  ? exercise.name
                  : (exercise.name && typeof exercise.name === 'object' && 'sq' in exercise.name)
                    ? (exercise.name as any).sq
                    : exercise.name_al}
              </Text>
            </View>

            {/* RIGHT ‑ weight input */}
            <TextInput
              ref={(el) => { inputRefs.current[index] = el; }}
              style={[styles.weightInput, isHighlighted && styles.weightInputHighlighted]}
              keyboardType="numeric"
              value={weight}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChangeText={handleChange}
              maxLength={5}
              placeholder="Pesha"
              placeholderTextColor="#9CA3AF"
              textAlign="center"
            />
          </View>
        );
      })}
    </ScrollView>
  );
};

/* -------------------------------------------------------------------------- */
/*                                   Styles                                   */
/* -------------------------------------------------------------------------- */

const styles = StyleSheet.create({
  /* --- scroll container --- */
  scrollView: { flex: 1 },
  scrollContainer: { paddingVertical: 12 },

  /* --- row --- */
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12, // bigger height
    paddingHorizontal: 14,
    borderRadius: 8,
    marginBottom: 8,
    minHeight: 60, // bigger clickable area
    backgroundColor: '#fff',
  },
  rowHighlighted: {
    backgroundColor: 'rgb(254, 247, 255)',
  },

  /* --- left side --- */
  leftSide: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  iconWrap: { justifyContent: 'center', alignItems: 'center', marginRight: 10 },
  iconBtn: {
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nameText: {
    fontSize: 16,
    color: 'rgb(55, 65, 81)',
    flexShrink: 1,
  },
  nameTextBold: {
    fontWeight: '500',
  },

  /* --- weight input --- */
  weightInput: {
    width: 70,
    height: 28,
    borderRadius: 6,
    paddingVertical: 0,
    paddingHorizontal: 6,
    includeFontPadding: false,
    fontSize: 16,
    fontWeight: '500',
    backgroundColor: '#fff',
    color: '#9CA3AF',
  },
  weightInputHighlighted: {
    backgroundColor: 'rgba(238,242,245,1)',
    color: 'rgb(55, 65, 81)',
    fontWeight: '500',
    fontSize: 16,
  },
});

export default ExerciseList;
