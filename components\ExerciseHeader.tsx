import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';

interface ExerciseHeaderProps {
  exerciseName: {
    sq?: string;
    en?: string;
  };
  description?: string;
  isDescriptionExpanded: boolean;
  onToggleDescription: () => void;
  timer: number;
  isTimerActive: boolean;
  onToggleTimer: () => void;
  sets?: number;
  reps?: number;
  restBetweenSets?: number;
}

const ExerciseHeader = ({ 
  exerciseName,
  description,
  isDescriptionExpanded,
  onToggleDescription,
  timer,
  isTimerActive,
  onToggleTimer,
  sets,
  reps,
  restBetweenSets
}: ExerciseHeaderProps) => {
  const displayName = exerciseName?.sq || exerciseName?.en || 'Exercise';

  return (
    <View style={styles.timerContainer}>
      <Text style={styles.exerciseTitle}>{displayName}</Text>
      
      <View style={styles.exerciseDetails}>
        <TouchableOpacity onPress={onToggleTimer} style={styles.timerTouchable}>
          <FontAwesome5
            name="stopwatch"
            size={18}
            color={isTimerActive ? '#FF69B4' : '#9CA3AF'}
          />
          <Text style={[styles.timerText, { color: isTimerActive ? '#FF69B4' : '#9CA3AF' }]}>
            {restBetweenSets} sec
          </Text>
        </TouchableOpacity>

        <View style={styles.statsContainer}>
          {sets && (
            <View style={styles.statItem}>
              <FontAwesome5 name="dumbbell" size={14} color="#666" />
              <Text style={styles.statText}>{sets} sets</Text>
            </View>
          )}
          {reps && (
            <View style={styles.statItem}>
              <Text style={styles.statText}>{reps} reps</Text>
            </View>
          )}
        </View>
      </View>

      {description && (
        <>
          <Text style={styles.description}>
            {isDescriptionExpanded
              ? description
              : description.length > 100
              ? `${description.substring(0, 100)}...`
              : description}
          </Text>
          {description.length > 100 && (
            <TouchableOpacity onPress={onToggleDescription}>
              <Text style={styles.readMoreText}>
                {isDescriptionExpanded ? 'See less' : 'See more'}
              </Text>
            </TouchableOpacity>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  timerContainer: {
    width: '100%',
    alignItems: 'flex-start',
    marginVertical: 8,
    marginLeft: 0,
    justifyContent: 'space-between',
  },
  exerciseTitle: {
    fontSize: 18, // Header font size
    fontWeight: '600',
    marginBottom: 8,
    color: '#1F2937',
  },
  exerciseDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timerTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  timerText: {
    fontSize: 14, // Subheader font size
    marginLeft: 6,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  statText: {
    fontSize: 14, // Exercise font size
    marginLeft: 4,
    color: '#666',
  },
  description: {
    fontSize: 16, // Exercise font size
    color: '#6B7280',
    marginBottom: 6,
  },
  readMoreText: {
    fontSize: 16, // Exercise font size
    color: '#E84479',
    fontWeight: '500',
    marginTop: 2,
  },
});

export default ExerciseHeader;