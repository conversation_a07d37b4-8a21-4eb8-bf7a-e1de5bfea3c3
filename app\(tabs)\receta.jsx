import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Platform, Linking } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { FontAwesome5 } from '@expo/vector-icons';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Animatable from 'react-native-animatable';
import { useFocusEffect, useRouter } from 'expo-router';
import { getWorkoutCount } from '../workoutStorage';
import useLastExercise from '../useLastExercise';
import useLanguage from '../../hooks/useLanguage';

import { SafeAreaView } from 'react-native-safe-area-context';

const LANGUAGE_KEY = 'appLanguage';

const ProfileScreen = () => {
  const [userName, setUserName] = useState('');
  const [totalTime, setTotalTime] = useState(0);
  const [workoutCount, setWorkoutCount] = useState(0);
  const { lastExercise } = useLastExercise();
  const safeLastExercise = lastExercise || { name: '', description: '' };
  const router = useRouter();
  const [language, setLanguage] = useState('en');
  const { t } = useLanguage();

  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const storedLang = await AsyncStorage.getItem(LANGUAGE_KEY);
        if (storedLang) setLanguage(storedLang);
      } catch {}
    };
    loadLanguage();
  }, []);

  const toggleLanguage = async () => {
    const newLang = language === 'en' ? 'al' : 'en';
    setLanguage(newLang);
    await AsyncStorage.setItem(LANGUAGE_KEY, newLang);
  };

  useFocusEffect(
    useCallback(() => {
      const fetchWorkoutCount = async () => {
        const count = await getWorkoutCount();
        setWorkoutCount(count);
      };
      fetchWorkoutCount();
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      const fetchTime = async () => {
        try {
          const storedTime = await AsyncStorage.getItem('timeSpent');
          const totalSeconds = storedTime ? parseInt(storedTime, 10) : 0;
          setTotalTime(Math.floor(totalSeconds / 60));
        } catch (error) {
          console.error('Error retrieving time spent:', error);
        }
      };
      fetchTime();
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      const fetchUserName = async () => {
        try {
          const storedUserName = await AsyncStorage.getItem('userName');
          if (storedUserName) {
            setUserName(storedUserName);
          } else {
            await AsyncStorage.setItem('userName', 'User');
            setUserName('User');
          }
        } catch (error) {
          await AsyncStorage.setItem('userName', 'User');
          setUserName('User');
        }
      };
      fetchUserName();
    }, [])
  );

  // Handler for logout
  const handleLogout = async () => {
    try {
      await AsyncStorage.clear();
      router.replace('/'); // Navigate to login screen
    } catch (e) {
      alert('Failed to log out.');
    }
  };

  // Handler for delete account
  const handleDeleteAccount = () => {
    // TODO: Implement actual delete account logic
    // No action for now
  };

  // Recipe data with proper structure
  const recipes = [
    {
      name: 'Healthy Recipe Collection',
      id: 'cookbook',
      description: 'A collection of nutritious recipes for your fitness journey',
    },
    {
      name: 'Quick & Easy Recipes',
      id: 'easy_recipes',
      description: 'Simple recipes that can be prepared in under 30 minutes',
    },
  ];

  // Handler for opening PDF using Sharing API



// ...

const handleOpenPdf = async (recipe) => {
  try {
    console.log("Opening recipe:", recipe.name);

    // Navigate to PdfViewer screen with recipe ID
    router.push({
      pathname: "/PdfViewer",
      params: {
        recipeId: recipe.id,
        recipeName: recipe.name
      },
    });
  } catch (error) {
    console.error("Error opening recipe:", error);
    alert(`Unable to open recipe: ${error.message}`);
  }
};

  return (
    <ScrollView
      contentContainerStyle={styles.container}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      decelerationRate="fast"
      bounces={false}
    >
      <LinearGradient
        colors={["#E08080", "#C14242"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{    borderBottomLeftRadius: 24,
          borderBottomRightRadius: 24,}}
      >
        <View  style={[styles.header]} >
          <View style={{ flex: 1 }}>
            <SafeAreaView>
            <Text style={[styles.headerTitle, { color: '#fff', fontSize: 16, fontWeight: 'bold' }]}>{t.profile}</Text>
            <Text style={{ fontFamily: 'DancingScript-VariableFont_wght', fontWeight: 'bold', fontSize: 22, color: '#fff', textAlign: 'left', marginTop: 8 }}>{userName}</Text>
            </SafeAreaView>
          </View>
          <TouchableOpacity
            style={{ marginLeft: 12, backgroundColor: '#fff', borderRadius: 20, width: 40, height: 40, alignItems: 'center', justifyContent: 'center' }}
            onPress={toggleLanguage}
            activeOpacity={0.7}
          >
            <Text style={{ fontSize: 22, textAlign: 'center' }}>{language === 'en' ? '🇬🇧' : '🇦🇱'}</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Logout and Delete Account Buttons */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 16, marginTop: 16 }}>
        <TouchableOpacity
          style={{ backgroundColor: '#fff', borderRadius: 20, paddingVertical: 10, paddingHorizontal: 20, marginRight: 8, borderWidth: 1, borderColor: '#C14242' }}
          onPress={handleLogout}
        >
          <Text style={{ color: '#C14242', fontWeight: 'bold' }}>{t.logout || 'Logout'}</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ backgroundColor: '#C14242', borderRadius: 20, paddingVertical: 10, paddingHorizontal: 20, marginLeft: 8 }}
          onPress={handleDeleteAccount}
        >
          <Text style={{ color: '#fff', fontWeight: 'bold' }}>{t.delete_account || 'Delete Account'}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.contentContainer}>
        <Animatable.View animation="fadeInUp" duration={1000} delay={500}>
          <Text style={styles.sectionTitle}>Your Progress</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statBox}>
              <Animatable.View animation="bounceIn" duration={1500} delay={700}>
                <View style={styles.iconContainer}>
                  <FontAwesome5 name="dumbbell" size={24} color="#C14242" />
                </View>
              </Animatable.View>
              <Text style={styles.statValue}>{workoutCount}</Text>
              <Text style={styles.statLabel}>{t.workouts_done}</Text>
            </View>

            <View style={styles.statBox}>
              <Animatable.View animation="bounceIn" duration={1500} delay={900}>
                <View style={styles.iconContainerLarge}>
                  <FontAwesome5 name="calendar-check" size={28} color="#C14242" />
                </View>
              </Animatable.View>
              <Text style={styles.statValue}>1</Text>
              <Text style={styles.statLabel}>{t.days_active}</Text>
              <View style={styles.streakIndicator}>
                <Text style={styles.streakText}>🔥</Text>
              </View>
            </View>
          </View>
        </Animatable.View>

        <Animatable.View animation="fadeInUp" duration={1000} delay={900} style={styles.fullWidthSection}>
          <Text style={styles.sectionTitle}>{t.recipes || 'Recipes'}</Text>
          <View style={styles.activityCard}>
            {recipes.length === 0 ? (
              <Text style={styles.noRecipesText}>{t.no_recipes}</Text>
            ) : (
              recipes.map((recipe) => (
                <TouchableOpacity 
                  key={recipe.name} 
                  style={styles.recipeItem} 
                  onPress={() => handleOpenPdf(recipe)}
                  activeOpacity={0.7}
                >
                  <View style={styles.recipeItemContent}>
                    <FontAwesome5 name="file-pdf" size={18} color="#C14242" style={styles.pdfIcon} />
                    <Text style={styles.recipeText}>{recipe.name}</Text>
                    <FontAwesome5 name="external-link-alt" size={14} color="#999" />
                  </View>
                </TouchableOpacity>
              ))
            )}
          </View>
        </Animatable.View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    backgroundColor: '#F0F4FF',
    paddingBottom: 60,
  },
  header: {
    backgroundColor: '#C14242',
    paddingBottom: 16,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    alignItems: 'center',
    shadowColor: '#C14242',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: 0,
    paddingBottom: 0,
  },
  headerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 0,
    marginTop: 0,
    textAlign: 'left',
  },
  userName: {
    color: 'white',
    fontSize: 22,
    fontFamily: 'DancingScript-VariableFont_wght',
    fontWeight: 'bold',
    marginBottom: 4,
    marginTop: 0,
    textAlign: 'left',
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  statBox: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 25,
    marginHorizontal: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 6 },
    shadowRadius: 12,
    elevation: 8,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#C14242',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  iconContainerLarge: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#C14242',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4,
  },
  fullWidthSection: {
    marginBottom: 25,
  },
  statBoxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 25,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 6 },
    shadowRadius: 12,
    elevation: 8,
  },
  statContent: {
    flex: 1,
    marginLeft: 20,
  },
  streakIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#C14242',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  streakText: {
    fontSize: 24,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activityCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 25,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowOffset: { width: 0, height: 6 },
    shadowRadius: 12,
    elevation: 8,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 15,
  },
  exerciseInfo: {
    paddingLeft: 10,
  },
  lastExerciseText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  exerciseDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 15,
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    alignSelf: 'flex-start',
  },
  completedText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '600',
    marginLeft: 5,
  },
  noActivityContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  noExerciseText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 5,
  },
  motivationText: {
    fontSize: 14,
    color: '#C14242',
    textAlign: 'center',
    fontWeight: '500',
  },
  langToggleBtn: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: 'rgba(0,0,0,0.08)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: Platform.OS === 'android' ? 0.2 : 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  recipeItem: {
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  recipeItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  pdfIcon: {
    marginRight: 15,
  },
  recipeText: {
    color: '#333',
    fontSize: 16,
    flex: 1,
    fontWeight: '500',
  },
  noRecipesText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default ProfileScreen;