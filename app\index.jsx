import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  ImageBackground, 
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Alert,
  StatusBar
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from '../api/apiClient';
import ProgramLoadingScreen from './(tabs)/home';
import { useFonts } from 'expo-font';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  // Load Dancing Script font
  const [fontsLoaded] = useFonts({
    'DancingScript-VariableFont_wght': require('../assets/fonts/DancingScript-VariableFont_wght.ttf'),
  });

  if (!fontsLoaded) {
    return <View style={{flex:1, justifyContent:'center', alignItems:'center'}}><Text>Loading...</Text></View>;
  }

  const handleLogin = async () => {
    console.log('Login button pressed');
    
    if (!email || !password) {
      setErrorMessage('Email and password are required.');
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      console.log('Attempting login with email:', email);
      const response = await apiClient.post('/auth/login', { email, password });
      console.log('Login response:', response);
      
      if (!response.token) {
        throw new Error('Invalid response from server: No token received');
      }

      await AsyncStorage.setItem('authToken', response.token);
      await AsyncStorage.setItem('userEmail', email);
      
      if (response.user) {
        await AsyncStorage.setItem('userData', JSON.stringify(response.user));
      }

      const storedToken = await AsyncStorage.getItem('authToken');
      console.log('Stored token:', storedToken ? 'Token stored successfully' : 'Failed to store token');
      
      router.replace('/programs');
    } catch (error) {
      console.error('Login error:', error);
      if (error.message.includes('Session expired')) {
        setErrorMessage('Your session has expired. Please login again.');
      } else {
        setErrorMessage(error.message || 'An error occurred. Please try again later.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    console.log('Forgot password pressed');
    router.push('/forgot-password');
  };

  const testTouch = () => {
    console.log('Test button pressed');
    Alert.alert('Test', 'Touch events are working!');
  };

  return (
    <View style={styles.outerContainer}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <SafeAreaView style={styles.container} edges={['top']}>
        {isLoading ? (
          <ProgramLoadingScreen showText={false} />
        ) : (
          <KeyboardAvoidingView 
            style={styles.keyboardContainer}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <ImageBackground
              source={require('../assets/images/Rectangle 23.png')}
              style={styles.backgroundImage}
              resizeMode="cover"
            >
              <LinearGradient
                colors={['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.4)']}
                locations={[0.5, 1]}
                style={styles.gradient}
                pointerEvents="none"
              />
              <View style={styles.contentContainer}>
                <View style={styles.logoContainer}>
                  <Text style={styles.logo}>Bodies By Xhes</Text>
                </View>
                <View style={styles.formContainer}>
                  <View style={styles.inputContainer}>
                    <TextInput
                      style={styles.input}
                      placeholder="Email"
                      placeholderTextColor="#4B5563"
                      value={email}
                      onChangeText={(text) => {
                        setEmail(text);
                      }}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      editable={true}
                      selectTextOnFocus={true}
                    />
                    <TextInput
                      style={styles.input}
                      placeholder="Password"
                      placeholderTextColor="#4B5563"
                      secureTextEntry
                      value={password}
                      onChangeText={(text) => {
                        setPassword(text);
                      }}
                      editable={true}
                      selectTextOnFocus={true}
                    />
                  </View>

                  {errorMessage && <Text style={styles.errorText}>{errorMessage}</Text>}

                  <TouchableOpacity
                    style={[styles.loginButton, isLoading && styles.loginButtonDisabled]}
                    onPress={handleLogin}
                    disabled={isLoading}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.loginButtonText}>
                      Log In
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity 
                    onPress={handleForgotPassword}
                    activeOpacity={0.7}
                    style={styles.forgotPasswordButton}
                  >
                    <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </ImageBackground>
          </KeyboardAvoidingView>
        )}
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#000', // This ensures no white gaps
  },
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Changed from #000 to transparent
  },
  keyboardContainer: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: '40%',
    zIndex: 1,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 250,
    width: '100%',
  },
  logo: {
    fontSize: 46,
    fontWeight: '800',
    fontFamily: 'DancingScript-VariableFont_wght',
    color: '#C14242',
    textAlign: 'center',
    textShadowColor: '#fff',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 12,
  },
  formContainer: {
    width: '100%',
    alignItems: 'center',
    paddingTop: 20,
    zIndex: 3,
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
  },
  inputContainer: {
    width: '100%',
    alignItems: 'center',
    zIndex: 3,
  },
  input: {
    width: '100%',
    maxWidth: 327,
    height: 44,
    backgroundColor: 'rgba(255, 255, 255, 0.98)', // Nearly opaque white
    paddingHorizontal: 12,
    marginBottom: 12,
    borderRadius: 8,
    fontSize: 14,
    color: '#000000', // Pure black text for maximum contrast
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4, // Android shadow
    zIndex: 3,
  },
  loginButton: {
    width: '100%',
    maxWidth: 327,
    height: 43,
    backgroundColor: '#C14242',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5, // Android shadow
    zIndex: 3,
  },
  loginButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  forgotPasswordButton: {
    marginTop: 10,
    padding: 10,
    zIndex: 3,
  },
  forgotPasswordText: {
    color: '#C14242',
    fontSize: 14,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  errorText: {
    color: '#EF4444', // Brighter red
    marginTop: 10,
    fontSize: 14,
    textAlign: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
});

export default Login;